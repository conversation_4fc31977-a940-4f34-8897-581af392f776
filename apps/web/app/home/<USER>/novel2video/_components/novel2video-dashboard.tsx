'use client';

import { useState } from 'react';

import { PlusIcon } from 'lucide-react';

import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';

import { CreateProjectDialog } from './create-project-dialog';
import { ProjectsList } from './projects-list';

export function Novel2VideoDashboard() {
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  return (
    <div className="space-y-8">
      {/* Header with action */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl font-semibold tracking-tight">
            <Trans i18nKey="novel2video:dashboard.yourProjects" />
          </h1>
          <p className="text-muted-foreground">
            <Trans i18nKey="novel2video:dashboard.projectsDescription" />
          </p>
        </div>

        <Button size="default" onClick={() => setShowCreateDialog(true)}>
          <PlusIcon className="mr-2 h-4 w-4" />
          <Trans i18nKey="novel2video:dashboard.createProject" />
        </Button>
      </div>

      {/* Projects list */}
      <ProjectsList />

      {/* Create project dialog */}
      <CreateProjectDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
      />
    </div>
  );
}

